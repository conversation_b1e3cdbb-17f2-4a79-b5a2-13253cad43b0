"use server";
import {
    type GenerateLoginPayloadParams,
    type VerifyLoginPayloadParams,
    createAuth,
} from "thirdweb/auth";
import { client } from "@/config/client";

const thirdwebAuth = createAuth({
    domain: process.env.NEXT_PUBLIC_THIRDWEB_AUTH_DOMAIN || "",
    client,
});

export async function generatePayload(payload: GenerateLoginPayloadParams) {
    return thirdwebAuth.generatePayload(payload);
}
export async function verifyPayload(payload: VerifyLoginPayloadParams) {
    return thirdwebAuth.verifyPayload(payload);
}