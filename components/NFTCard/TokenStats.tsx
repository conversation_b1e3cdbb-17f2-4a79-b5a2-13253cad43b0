// components/NFTCard/TokenStats.tsx
'use client';
import { TokenData } from './types';

export const TokenStats = ({ tokenData }: { tokenData: TokenData }) => {
    // Calculate days remaining
    const daysRemaining = Math.ceil((new Date(tokenData.validUntil).getTime() - Date.now()) / (1000 * 60 * 60 * 24));

    const getRarityTextColor = (rarity: string) => {
        switch (rarity) {
            case 'Legendary': return 'text-purple-300';
            case 'Epic': return 'text-blue-300';
            case 'Rare': return 'text-cyan-300';
            default: return 'text-amber-300';
        }
    };

    const getRarityBorderColor = (rarity: string) => {
        switch (rarity) {
            case 'Legendary': return 'border-purple-500/30';
            case 'Epic': return 'border-blue-500/30';
            case 'Rare': return 'border-cyan-500/30';
            default: return 'border-amber-500/30';
        }
    };

    const getRarityGradient = (rarity: string) => {
        switch (rarity) {
            case 'Legendary': return 'from-purple-500/10 to-pink-500/10';
            case 'Epic': return 'from-blue-500/10 to-indigo-500/10';
            case 'Rare': return 'from-cyan-500/10 to-teal-500/10';
            default: return 'from-amber-500/10 to-orange-500/10';
        }
    };

    return (
        <div className="px-2 pb-4">
            <div className="grid grid-cols-2 gap-3 mb-4">
                {/* Redemptions */}
                <div className={`bg-gray-800/30 rounded-xl p-3 border ${getRarityBorderColor(tokenData.rarity)}`}>
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400 text-xs">Redemptions</span>
                        <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className={`${getRarityTextColor(tokenData.rarity)} font-bold text-lg`}>
                        {tokenData.redemptions}/{tokenData.maxRedemptions}
                    </p>
                    <div className="w-full bg-gray-700 rounded-full h-1.5 mt-2">
                        <div
                            className="bg-gradient-to-r from-green-400 to-green-500 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${(tokenData.redemptions / tokenData.maxRedemptions) * 100}%` }}
                        />
                    </div>
                </div>

                {/* Value */}
                <div className={`bg-gray-800/30 rounded-xl p-3 border ${getRarityBorderColor(tokenData.rarity)}`}>
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400 text-xs">Value</span>
                        <div className="flex items-center">
                            <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <p className={`${getRarityTextColor(tokenData.rarity)} font-bold text-lg flex items-center justify-center`}>
                        {tokenData.value}
                        <span className="ml-2 text-xs bg-amber-500/20 px-2 py-0.5 rounded-full">
                            {tokenData.rarity === 'Legendary' ? '5★' :
                                tokenData.rarity === 'Epic' ? '4★' :
                                    tokenData.rarity === 'Rare' ? '3★' : '2★'}
                        </span>
                    </p>
                </div>
            </div>

            {/* Expiry */}
            <div className={`bg-gradient-to-r ${getRarityGradient(tokenData.rarity)} border ${getRarityBorderColor(tokenData.rarity)} rounded-xl p-3 mb-3`}>
                <div className="flex items-center justify-between mb-1">
                    <span className="text-gray-200 text-xs">Valid Until</span>
                    <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                </div>
                <p className="text-white font-semibold text-sm">{tokenData.validUntilFormatted}</p>
                <p className={`text-xs mt-1 ${daysRemaining <= 3 ? 'text-red-400' : 'text-gray-200'}`}>
                    {Math.floor(daysRemaining)} {Math.floor(daysRemaining) === 1 ? 'day' : 'days'} remaining
                </p>
            </div>

            {/* Instructions */}
            <div className={`bg-gradient-to-r ${getRarityGradient(tokenData.rarity)} border ${getRarityBorderColor(tokenData.rarity)} rounded-xl p-3`}>
                <div className="flex items-start space-x-2">
                    <svg className="w-4 h-4 text-amber-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <div>
                        <p className="text-amber-400 font-semibold text-xs mb-1">How to Redeem</p>
                        <p className="text-gray-300 text-xs leading-relaxed">
                            Present this NFT at any participating Project Moja location to redeem your coffee. Each scan consumes one redemption.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};